import React, { useState, useCallback, useEffect } from 'react';
import { Modal, Button, Breadcrumb } from 'antd';
import { FolderOutlined, HeartFilled } from '@ant-design/icons';
import styles from './index.module.scss';
import { useRequest, useTheme } from 'ahooks';
import { Toast } from '@/components/Toast/manager';
import { getPoolInfo, listDirectory, StoragePool } from '@/api/fatWall';
import { PreloadImage } from '@/components/Image';
import close from '@/Resources/icon/close.png';
import close_white from '@/Resources/icon/close_white.png';

interface FileItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  time?: string;
  path: string;
  dataDir?: string; // 存储池的data_dir
  isDirectory: boolean;
  isLiked?: boolean;
  itemCount?: number;
}

interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

interface FileSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (path: string, displayPath?: string) => void;
  title?: string;
}

const FileSelector: React.FC<FileSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  title = '选择来源'
}) => {
  // 当前文件路径（面包屑）
  const [currentPath, setCurrentPath] = useState<BreadcrumbItem[]>([]);

  // 当前显示的文件列表
  const [currentFiles, setCurrentFiles] = useState<FileItem[]>([]);

  // 存储池列表
  const [storagePools, setStoragePools] = useState<StoragePool[]>([]);

  // webDAV配置信息
  const [webDAVConfig, setWebDAVConfig] = useState<{
    alias_root: string;
  } | null>(null);

  // 当前调用 list_directory 时传递的 path.parent 值
  const [currentPathParent, setCurrentPathParent] = useState<string>('');

  // 选中的文件夹路径（单选）
  const [selectedPath, setSelectedPath] = useState<string>('');

  // 是否已经初始化到第一个存储池
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  const { isDarkMode } = useTheme() as any;
  // 获取存储池信息
  const { run: fetchPoolInfo, loading: poolLoading } = useRequest(
    getPoolInfo,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          setStoragePools(response.data.internal_pool);

          // 保存webDAV配置
          if (response.data.webDAV) {
            setWebDAVConfig({
              alias_root: response.data.webDAV.alias_root
            });
          }

          // 自动进入第一个存储池
          if (response.data.internal_pool.length > 0 && !isInitialized) {
            const firstPool = response.data.internal_pool[0];

            // 设置面包屑为第一个存储池
            const newBreadcrumb: BreadcrumbItem = {
              id: 'pool_0',
              name: firstPool.name,
              path: firstPool.data_dir
            };
            setCurrentPath([newBreadcrumb]);

            // 构造正确的path.parent：data_dir + alias_root
            let pathParent = firstPool.data_dir;
            if (response.data.webDAV?.alias_root) {
              const dataDir = firstPool.data_dir.endsWith('/') ? firstPool.data_dir.slice(0, -1) : firstPool.data_dir;
              const aliasRoot = response.data.webDAV.alias_root;
              pathParent = aliasRoot + dataDir;
            }

            setCurrentPathParent(pathParent);
            setIsInitialized(true);

            // 获取第一个存储池的目录列表
            fetchDirectory({
              path: {
                parent: pathParent,
                recursion: false
              }
            });
          } else {
            // 如果没有存储池或已经初始化过，显示存储池列表
            const poolItems: FileItem[] = response.data.internal_pool.map((pool: StoragePool, index: number) => ({
              id: `pool_${index}`,
              name: pool.name,
              type: 'folder' as const,
              time: '',
              path: pool.data_dir,
              dataDir: pool.data_dir,
              isDirectory: true
            }));
            setCurrentFiles(poolItems);
            setCurrentPath([]);
          }
        }
      },
      onError: (error) => {
        console.error('获取存储池信息失败：', error);
        Toast.show('获取存储池信息失败，请重试', { duration: 2000 });
        setCurrentFiles([]);
        setCurrentPath([]);
      },
    }
  );

  // 获取目录列表
  const { run: fetchDirectory, loading: directoryLoading } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          // 转换API返回的文件列表为本地格式
          const files: FileItem[] = response.data.files
            .filter((file: any) => file.xattr.directory) // 只显示文件夹
            .map((file: any, index: number) => ({
              id: `file_${index}`,
              name: file.name,
              type: 'folder' as const,
              time: new Date(parseInt(file.modified_time)).toLocaleDateString(),
              path: `${file.parent}/${file.name}`,
              isDirectory: file.xattr.directory,
              isLiked: file.xattr.favorite
            }));
          setCurrentFiles(files);
        }
      },
      onError: (error) => {
        console.error('获取目录列表失败：', error);
        Toast.show('获取目录失败，请重试', { duration: 2000 });
        // 显示空列表或返回上级目录
        setCurrentFiles([]);
      },
    }
  );

  // 初始化时获取存储池信息
  useEffect(() => {
    if (visible) {
      setIsInitialized(false); // 重置初始化状态
      setSelectedPath(''); // 清空选中状态
      fetchPoolInfo({});
    }
  }, [visible, fetchPoolInfo]);

  // 进入文件夹
  const navigateToFolder = useCallback((folder: FileItem) => {
    // 清空选中状态，因为进入了新的层级
    setSelectedPath('');

    if (currentPath.length === 0) {
      // 如果是顶层（选择存储池），添加到面包屑并获取该存储池的目录
      const newBreadcrumb: BreadcrumbItem = {
        id: folder.id,
        name: folder.name,
        path: folder.path
      };
      setCurrentPath([newBreadcrumb]);

      // 构造正确的path.parent：data_dir + alias_root，避免双斜杠
      let pathParent = folder.path;
      if (webDAVConfig?.alias_root && folder.dataDir) {
        // 确保data_dir末尾没有斜杠
        const dataDir = folder.dataDir.endsWith('/') ? folder.dataDir.slice(0, -1) : folder.dataDir;
        const aliasRoot = webDAVConfig.alias_root;
        pathParent = aliasRoot + dataDir;
      }

      // 保存当前的 path.parent 值
      setCurrentPathParent(pathParent);

      // 调用目录列表API
      fetchDirectory({
        path: {
          parent: pathParent,
          recursion: false
        }
      });
    } else {
      // 如果已经在某个目录中，继续深入
      const newBreadcrumb: BreadcrumbItem = {
        id: folder.id,
        name: folder.name,
        path: folder.path
      };
      setCurrentPath(prev => [...prev, newBreadcrumb]);

      // 保存当前的 path.parent 值
      setCurrentPathParent(folder.path);

      // 调用目录列表API
      fetchDirectory({
        path: {
          parent: folder.path,
          recursion: false
        }
      });
    }
  }, [currentPath, fetchDirectory, webDAVConfig]);

  // 通过面包屑导航到指定路径
  const navigateToBreadcrumb = useCallback((index: number) => {
    // 清空选中状态，因为切换了层级
    setSelectedPath('');

    // 如果点击"存储池"（index为-1），直接返回顶层
    if (index === -1) {
      setCurrentPath([]);
      setCurrentPathParent(''); // 清空 path.parent
      fetchPoolInfo({});
      return;
    }

    const newPath = currentPath.slice(0, index + 1);
    setCurrentPath(newPath);

    if (newPath.length === 0) {
      // 返回到顶层，显示存储池列表
      setCurrentPathParent(''); // 清空 path.parent
      fetchPoolInfo({});
    } else {
      // 获取指定路径的目录列表
      const targetPath = newPath[newPath.length - 1].path;

      // 如果是第一层（存储池层级），需要构造特殊的路径
      if (newPath.length === 1) {
        // 找到对应的存储池数据
        const poolData = storagePools.find(pool => pool.name === newPath[0].name);
        if (poolData && webDAVConfig?.alias_root) {
          // 构造正确的path.parent：data_dir + alias_root，避免双斜杠
          const dataDir = poolData.data_dir.endsWith('/') ? poolData.data_dir.slice(0, -1) : poolData.data_dir;
          const aliasRoot = webDAVConfig.alias_root;
          const pathParent = aliasRoot + dataDir;

          // 保存当前的 path.parent 值
          setCurrentPathParent(pathParent);

          fetchDirectory({
            path: {
              parent: pathParent,
              recursion: false
            }
          });
        }
      } else {
        // 普通的文件夹层级
        // 保存当前的 path.parent 值
        setCurrentPathParent(targetPath);

        fetchDirectory({
          path: {
            parent: targetPath,
            recursion: false
          }
        });
      }
    }
  }, [currentPath, fetchPoolInfo, fetchDirectory, storagePools, webDAVConfig]);

  // 选择当前路径
  const handleSelect = useCallback(() => {
    if (!selectedPath) {
      Toast.show('请选择一个文件夹', { duration: 2000 });
      return;
    }

    // 找到对应的文件项
    const file = currentFiles.find(f => f.path === selectedPath);
    let displayPath = selectedPath;

    if (file) {
      // 构建完整的显示路径：存储池名称 + 文件夹名称
      const poolName = currentPath.length > 0 ? currentPath[0].name : '';
      displayPath = poolName ? `${poolName}/${file.name}` : file.name;
    }

    onSelect(selectedPath, displayPath);
    onClose();

    // 重置状态
    setCurrentPath([]);
    setCurrentFiles([]);
    setCurrentPathParent('');
    setSelectedPath('');
  }, [selectedPath, currentFiles, currentPath, onSelect, onClose]);

  // 关闭弹窗时重置状态
  const handleClose = useCallback(() => {
    setCurrentPath([]);
    setCurrentFiles([]);
    setCurrentPathParent('');
    setSelectedPath('');
    setIsInitialized(false);
    onClose();
  }, [onClose]);

  // 生成面包屑项
  const breadcrumbItems = [
    {
      title: (
        <span
          onClick={() => navigateToBreadcrumb(-1)}
          style={{ cursor: 'pointer' }}
        >
          存储池
        </span>
      )
    },
    ...currentPath.map((pathItem, index) => ({
      title: (
        <span
          onClick={() => navigateToBreadcrumb(index)}
          style={{ cursor: 'pointer' }}
        >
          {pathItem.name}
        </span>
      )
    }))
  ];

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleClose}
      closeIcon={<PreloadImage style={{width: '20px', height: '20px'}} src={isDarkMode ?  close_white:close } alt="关闭" />}
      footer={null}
      width={600}
      centered
      className={styles.modal}
      destroyOnClose={true}
      maskClosable={false}
    >
      <div className={styles.container}>
        {/* 面包屑 */}
        <div className={styles.breadcrumbContainer}>
          <Breadcrumb
            items={breadcrumbItems}
            separator=">"
            className={styles.breadcrumb}

          />
        </div>

        {/* 加载状态 */}
        {(poolLoading || directoryLoading) && (
          <div className={styles.loadingContainer}>
            <span>加载中...</span>
          </div>
        )}

        {/* 文件列表 */}
        <div className={styles.fileList}>
          {currentFiles.map(file => (
            <div
              key={file.id}
              className={`${styles.fileItem} ${selectedPath === file.path ? styles.selected : ''}`}
            >
              <div className={styles.fileIcon}>
                <FolderOutlined />
              </div>
              <div
                className={styles.fileInfo}
                onClick={() => {
                  // 点击文件内容进入下一级
                  if (file.type === 'folder') {
                    navigateToFolder(file);
                  }
                }}
              >
                <div className={styles.fileName}>
                  {file.name}
                  {file.isLiked && <HeartFilled className={styles.heartIcon} />}
                </div>
                <div className={styles.fileDetails}>
                  {file.time && `${file.time}`}
                  {file.itemCount && ` | ${file.itemCount}项`}
                </div>
              </div>
              {/* 选中状态指示器 */}
              <div className={styles.checkboxContainer}>
                <div
                  className={`${styles.customCheckbox} ${selectedPath === file.path ? styles.checked : ''}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    // 只有点击checkbox才选中
                    if (selectedPath === file.path) {
                      setSelectedPath('');
                    } else {
                      setSelectedPath(file.path);
                    }
                  }}
                />
              </div>
            </div>
          ))}

          {!poolLoading && !directoryLoading && currentFiles.length === 0 && (
            <div className={styles.emptyState}>
              <span>该目录下没有文件夹</span>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className={styles.footer}>
          <div className={styles.cancelButton}>
            <Button
              className={styles.cancelButton}
              onClick={handleClose}
            >
              取消
            </Button>
          </div>
          <div className={styles.confirmButton}>
            <Button
              onClick={handleSelect}
              disabled={!selectedPath}
              type="primary"
            >
              确定为来源路径
            </Button>
          </div>

        </div>
      </div>
    </Modal>
  );
};

export default FileSelector; 