.modal {
  :global(.ant-modal-content) {
    border-radius: 20px;
    padding: 0;
    overflow: hidden;
    background-color: var(--desktop-modal-bg-color);
  }
  
  :global(.ant-modal-header) {
    padding: 20px 24px 16px;
    text-align: center;
    background-color: var(--desktop-modal-bg-color);
    
    :global(.ant-modal-title) {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
    }
  }
  :global(.ant-modal-close){
    left: 15px;
}
  :global(.ant-modal-body) {
    padding: 0;
  }

}

.container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.breadcrumbContainer {
  padding: 16px 24px;
  background-color: var(--desktop-modal-bg-color);
}

.breadcrumb {
    :global(.ant-breadcrumb-link:last-child) {
        background: var(--card-active-background-color);
        color: var(--add-lib-text-color);
        padding: 4px 10px;
        border-radius: 20px;
      }
      :global(.ant-breadcrumb-link) {
        color: var(--text-color);
        transition: all 0.3s;
        background: var(--card-active-background-color);
        
        &:hover {
          color: rgba(236, 162, 0, 0.2);

        }
        &:active{
          background: rgba(236, 162, 0, 0.2);
        }
      }
      :global(li:last-child .ant-breadcrumb-link) {
        background: #FFF4DD;
        color: #EBA41B;
        padding: 4px 10px;
        border-radius: 20px;
      }
}

.fileList {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
  gap: 8px;
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
}

.fileItem {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-radius: 16px;
  transition: background-color 0.2s;

  &.selected {
    background-color: var(--card-active-background-color);
    border-radius: 16px;
  }
}

.fileIcon {
  width: 40px;
  height: 40px;
  background-color: #ffa940;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  
  :global(.anticon) {
    color: white;
    font-size: 20px;
  }
}

.fileInfo {
  flex: 1;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.fileName {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
  font-family: MiSans;
  display: flex;
  align-items: center;
  gap: 8px;
}

.heartIcon {
  color: #ff4d4f;
  font-size: 14px;
}

.fileTime {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.itemCount {
  font-size: 12px;
  color: #999;
}

.fileDetails {
  font-size: 12px;
  color: #999;
}

.checkboxContainer {
  margin-left: 12px;
}

.customCheckbox {
  width: 20px;
  height: 20px;
  border: 2px solid #d9d9d9;
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;

  &.checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    &::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 2px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }

  &:hover {
    border-color: var(--primary-color);
  }
}

.footer {
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  :global(.ant-btn-variant-outlined) {
    &:disabled{
      background: var(--primary-color);
      color: #fff;
      opacity: 0.5;
    }
  }
  :global(.ant-btn-variant-outlined:not(:disabled):hover) {
    opacity: 0.7;
    background: var(--primary-color);
  }
}


.cancelButton {
  flex: 1;
  border-radius: 16px;
  width: 239px;
  height: 50px;
  font-weight: 500;
  font-size: 16px;
  background-color: var(--componentcard-btn-bg-color);
  border: none;
  color: var(--text-color);
  font-family: MiSans;
  :global(.ant-btn-variant-outlined){
    height: 100%;
    width: 100%;
  }
  :global(.ant-btn-variant-outlined:not(:disabled):hover) {
    opacity: 0.5;
    background: var(--table-hover-bg);
    color: var(--text-color);
  }
  
}

.confirmButton {
  flex: 1;
  height: 50px;
  border-radius: 16px;
  font-size: 17px;
  width: 239px;
  font-weight: 500;
  font-family: MiSans;
  height: 50px;
  color: #fff;
  background-color: #3482FF;
//   border-color: #1890ff;
:global(.ant-btn-variant-outlined){
  height: 100%;
  width: 100%;
  background-color: var(--primary-color);
  border-radius: 16px;
  color: #fff;
  border: none;
}
  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }

} 