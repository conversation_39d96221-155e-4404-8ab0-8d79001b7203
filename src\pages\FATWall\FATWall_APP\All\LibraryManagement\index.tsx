import NavigatorBar from "@/components/NavBar"
import { useHistory, useRouteMatch, useLocation } from "react-router-dom";
import styles from "./index.module.scss";
import { useEffect, useMemo, useState, useCallback, useRef } from "react";
import ListCard, { IListCard } from "@/components/ListCard";
import cover from '@/Resources/dashboardCard/Frame.png';
import { Toast, Divider } from 'antd-mobile';
import request from '@/request';
import { modalShow } from '@/components/List';
import { useLibraryListApp } from "../..";
import { useTheme } from "@/utils/themeDetector";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import { PreloadImage } from "@/components/Image";
import addIcon_light from '@/Resources/icon/addIcon_light.png';
import addIcon_dark from '@/Resources/icon/addIcon_dark.png';
import { px2rem } from '@/utils/setRootFontSize';
import { useLibraryList } from "@/pages/FATWall/FATWall_PC";
import { getPoolInfo, StoragePool, WebDAVConfig } from '@/api/fatWall';

// 扩展的媒体库卡片类型，包含额外的媒体库信息
interface ILibraryCard extends IListCard {
  lib_id?: any;
  share_from?: number;
  scan_percent?: number;
  scan_status?: string;
  scan_error_code?: string;
  onDelete?: (lib_id?: number) => void;
  onScan?: (lib_id?: number) => void; // 添加扫描回调
  // 编辑相关的原始数据
  scan_path?: string[];
  tv_visable?: number;
  share2who_list?: (string | number)[];
}

const LibraryManagement = () => {
  const history = useHistory();
  const routeMatch = useRouteMatch();
  const location = useLocation<{ lib_id?: number; title?: string; fromLibrary?: boolean }>();
  const [myselfCreate, setMyselfCreate] = useState<ILibraryCard[]>([]); // 自己创建的媒体库
  const [otherShare, setOtherShare] = useState<ILibraryCard[]>([]); // 别人分享的媒体库
  const [loading, setLoading] = useState(false); // 手动控制loading状态
  const [, setIsPolling] = useState(false); // 标识是否正在轮询
  const path = routeMatch.path.split('/')[1];
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null); // 轮询定时器引用
  const [completedScans, setCompletedScans] = useState<Set<string>>(new Set()); // 跟踪刚完成扫描的媒体库
  const completedScanTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map()); // 跟踪完成扫描的定时器
  const previousLibraryStatusRef = useRef<Map<string, string>>(new Map()); // 存储之前的扫描状态
  const { isDarkMode } = useTheme();

  const libParams = useLibraryList(); // 从pc context中获取库列表
  const libParamsApp = useLibraryListApp(); // 从app context中获取库列表

  // 存储池信息状态
  const [storagePools, setStoragePools] = useState<StoragePool[]>([]);
  const [webDAVConfig, setWebDAVConfig] = useState<WebDAVConfig | null>(null);

  // 获取存储池信息
  const fetchPoolInfo = useCallback(async () => {
    try {
      const response = await getPoolInfo({});
      if (response.code === 0 && response.data) {
        setStoragePools(response.data.internal_pool);
        if (response.data.webDAV) {
          setWebDAVConfig(response.data.webDAV);
        }
      }
    } catch (error) {
      console.error('获取存储池信息失败：', error);
    }
  }, []);



  // 停止轮询
  const stopPolling = useCallback(() => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
    setIsPolling(false); // 标记轮询已停止
  }, []);

  // 清理完成扫描的定时器
  const clearCompletedScanTimer = useCallback((libId: string) => {
    const timer = completedScanTimersRef.current.get(libId);
    if (timer) {
      clearTimeout(timer);
      completedScanTimersRef.current.delete(libId);
    }
  }, []);

  // 处理扫描完成状态
  const handleScanCompleted = useCallback((libId: string) => {
    setCompletedScans(prev => new Set(prev).add(libId));

    // 清理之前的定时器（如果存在）
    clearCompletedScanTimer(libId);

    // 3秒后移除完成状态
    const timer = setTimeout(() => {
      setCompletedScans(prev => {
        const newSet = new Set(prev);
        newSet.delete(libId);
        return newSet;
      });
      completedScanTimersRef.current.delete(libId);
    }, 3000);

    completedScanTimersRef.current.set(libId, timer);
  }, [clearCompletedScanTimer]);

  // 检查是否有正在扫描的媒体库
  const hasScanning = useCallback((libraries: ILibraryCard[]) => {
    return libraries.some(lib => lib.scan_status === '扫描中');
  }, []);

  // 根据扫描状态生成副标题文本
  const getSubtitleText = useCallback((lib: any) => {
    const { scan_status, scan_percent, scan_error_code, create_time, update_time, lib_id } = lib;

    // 如果是刚完成扫描的媒体库，显示"扫描完成"
    if (completedScans.has(lib_id?.toString())) {
      return '扫描完成';
    }

    switch (scan_status) {
      case '扫描中':
        return `扫描中... ${Math.round(scan_percent || 0)}%`;
      case '扫描完成':
        return `最近更新 ${new Date(Number(update_time || create_time) * 1000).toLocaleDateString()}`;
      case '扫描异常':
        return `扫描失败：${scan_error_code || '未知错误'}`;
      case '未开始':
      default:
        return `最近更新 ${new Date(Number(update_time || create_time) * 1000).toLocaleDateString()}`;
    }
  }, [completedScans]);

  // 获取媒体库数据
  const fetchLibraryData = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true);
      }

      const response = await request.post('/mediacenter/lib_list', {
        filter: {}
      }, {
        showLoading: showLoading // 传递showLoading参数到request.js
      });

      if (response.code === 0 && response.data) {
        // 由于API实际返回的字段名是 my_libs，需要类型断言
        const apiData = response.data as any;

        // 检查新完成的扫描 - 使用之前存储的状态进行比较
        const allLibs = [...(apiData.my_libs?.libs || []), ...(apiData.share2me?.libs || [])];
        allLibs.forEach((lib: any) => {
          const libIdStr = lib.lib_id?.toString();
          const previousStatus = previousLibraryStatusRef.current.get(libIdStr);

          // 如果之前是扫描中，现在是扫描完成，则触发完成状态
          if (previousStatus === '扫描中' && lib.scan_status === '扫描完成' && !completedScans.has(libIdStr)) {
            handleScanCompleted(libIdStr);
          }

          // 更新状态记录
          previousLibraryStatusRef.current.set(libIdStr, lib.scan_status);
        });

        // 转换我创建的媒体库数据
        const myLibraryData: (ILibraryCard & { name: string, lib_id: number, count: number })[] = (apiData.my_libs?.libs || []).map((lib: any) => ({
          avatar: lib.poster && lib.poster.length > 0 ? lib.poster[0] : cover, // 使用第一张海报，如果没有则使用默认封面
          title: lib.name,
          subtitle: getSubtitleText(lib), // 根据扫描状态显示不同的副标题
          type: 'more' as const,
          lib_id: lib.lib_id,
          scan_percent: lib.scan_percent,
          scan_status: lib.scan_status,
          scan_error_code: lib.scan_error_code,
          // 编辑相关的原始数据
          scan_path: lib.scan_path,
          tv_visable: lib.tv_visable,
          share2who_list: lib.share2who_list || [],
          onScan: handleScanLibrary,
          name: lib.name,
          count: lib.media_count
        }));

        // 转换分享给我的媒体库数据
        const sharedLibraryData: (ILibraryCard & { name: string, lib_id: number, count: number })[] = (apiData.share2me?.libs || []).map((lib: any) => ({
          avatar: lib.poster && lib.poster.length > 0 ? lib.poster[0] : cover,
          title: lib.name,
          subtitle: getSubtitleText(lib), // 根据扫描状态显示不同的副标题
          type: 'more' as const,
          lib_id: lib.lib_id,
          share_from: lib.share_from,
          scan_percent: lib.scan_percent,
          scan_status: lib.scan_status,
          scan_error_code: lib.scan_error_code,
          tv_visable: lib.tv_visable,
          onDelete: handleExitLibraryShare, // 添加退出回调
          name: lib.name,
          count: lib.media_count
        }));

        setMyselfCreate(myLibraryData);
        setOtherShare(sharedLibraryData);

        // 检查是否还有正在扫描的媒体库，决定是否需要轮询
        const allLibraries = [...myLibraryData, ...sharedLibraryData];
        if (hasScanning(allLibraries)) {
          // 如果有正在扫描的媒体库且当前没有轮询，则开始轮询
          if (!pollingTimerRef.current) {
            startPolling();
          }
        } else {
          // 如果没有正在扫描的媒体库，则停止轮询
          stopPolling();
        }
        console.log('====================================');
        console.log(123123);
        console.log('====================================');
        libParams.setLibs(allLibraries) // 更新库信息
        libParamsApp.setLibs(allLibraries);
      } else {
        // API返回数据格式不正确时清空列表
        console.warn('API返回数据格式不正确');
        setMyselfCreate([]);
        setOtherShare([]);
        stopPolling(); // 停止轮询
        libParams.setLibs([]) // 更新库信息
        libParamsApp.setLibs([]);

        Toast.show({
          content: 'API返回数据格式不正确',
          position: 'bottom',
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('获取媒体库数据失败：', error);
      // 接口失败时清空列表
      setMyselfCreate([]);
      setOtherShare([]);
      stopPolling(); // 停止轮询

      if (showLoading) {
        Toast.show({
          content: '获取媒体库数据失败，请重试',
          position: 'bottom',
          duration: 2000,
        });
      }
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasScanning, completedScans, libParams.setLibs, libParamsApp.setLibs, handleScanCompleted, getSubtitleText, stopPolling]);

  // 退出媒体库分享
  const handleExitLibraryShare = useCallback(async (lib_id?: number) => {
    if (!lib_id) {
      console.error('退出媒体库分享失败：缺少lib_id');
      Toast.show({
        content: '参数错误，无法退出媒体库',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }

    try {
      setLoading(true);
      const response = await request.post('/mediacenter/lib_exit_share', { lib_id }, {
        showLoading: true // 用户操作显示loading
      });

      if (response.code === 0) {
        // 退出成功，从列表中移除该媒体库
        setOtherShare(prev => prev.filter(item => item.lib_id !== lib_id));

        Toast.show({
          content: "退出成功",
          position: "bottom",
          duration: 2000,
        });

        // 刷新媒体库列表
        fetchLibraryData(false); // 不显示loading
      } else {
        Toast.show({
          content: response.result || "退出失败，请重试",
          position: "bottom",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('退出媒体库分享失败：', error);
      Toast.show({
        content: '网络异常，退出失败',
        position: 'bottom',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  }, [fetchLibraryData]);

  // 开始轮询扫描状态
  const startPolling = useCallback(() => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
    }

    setIsPolling(true); // 标记开始轮询
    // 每3秒轮询一次，不显示loading
    pollingTimerRef.current = setInterval(() => {
      fetchLibraryData(false); // 轮询时不显示loading
    }, 3000);
  }, [fetchLibraryData]);

  // 处理扫描媒体库
  const handleScanLibrary = useCallback(async (lib_id?: number) => {
    if (!lib_id) {
      console.error('扫描媒体库失败：缺少lib_id');
      Toast.show({
        content: '参数错误，无法扫描媒体库',
        position: 'bottom',
        duration: 2000,
      });
      return;
    }

    try {
      setLoading(true);
      const response = await request.post('/mediacenter/lib_scan', { lib_id }, {
        showLoading: true // 用户操作显示loading
      });

      if (response.code === 0) {
        Toast.show({
          content: "扫描任务已创建",
          position: "bottom",
          duration: 2000,
        });

        // 开始轮询扫描状态
        startPolling();
      } else {
        Toast.show({
          content: response.result || "创建扫描任务失败",
          position: "bottom",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('创建扫描任务失败：', error);
      Toast.show({
        content: '网络异常，创建扫描任务失败',
        position: 'bottom',
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  }, [startPolling]);

  // 更新fetchLibraryData中的startPolling调用
  useEffect(() => {
    // 这个effect用于处理fetchLibraryData中需要调用startPolling的情况
    // 通过监听数据变化来决定是否需要轮询
    const allLibraries = [...myselfCreate, ...otherShare];
    if (hasScanning(allLibraries)) {
      if (!pollingTimerRef.current) {
        startPolling();
      }
    } else {
      stopPolling();
    }
  }, [myselfCreate, otherShare, hasScanning, startPolling, stopPolling]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      stopPolling();
      // 清理所有完成扫描的定时器
      completedScanTimersRef.current.forEach(timer => clearTimeout(timer));
      // eslint-disable-next-line react-hooks/exhaustive-deps
      completedScanTimersRef.current.clear();
    };
  }, [stopPolling]);

  // 处理编辑媒体库
  const handleEditLibrary = useCallback((item: ILibraryCard) => {
    console.log('编辑媒体库：', item);

    // 预处理显示路径，避免编辑页面闪烁
    const processedScanPath = (item.scan_path || []).map(originalPath => {
      // 内联路径转换逻辑
      if (!originalPath || !webDAVConfig || storagePools.length === 0) {
        return { path: originalPath, displayName: originalPath };
      }

      // 查找匹配的存储池
      const matchingPool = storagePools.find(pool => {
        const aliasRoot = webDAVConfig.alias_root || '';
        const dataDir = pool.data_dir.endsWith('/') ? pool.data_dir.slice(0, -1) : pool.data_dir;
        const poolPath = aliasRoot + dataDir;
        return originalPath.startsWith(poolPath);
      });

      if (matchingPool) {
        const aliasRoot = webDAVConfig.alias_root || '';
        const dataDir = matchingPool.data_dir.endsWith('/') ? matchingPool.data_dir.slice(0, -1) : matchingPool.data_dir;
        const poolPath = aliasRoot + dataDir;

        // 移除存储池路径前缀，获取相对路径
        const relativePath = originalPath.substring(poolPath.length);

        // 构建显示路径：存储池名称 + 相对路径
        if (relativePath && relativePath !== '/') {
          const cleanRelativePath = relativePath.startsWith('/') ? relativePath.substring(1) : relativePath;
          return { path: originalPath, displayName: `${matchingPool.name}/${cleanRelativePath}` };
        } else {
          return { path: originalPath, displayName: matchingPool.name };
        }
      }

      return { path: originalPath, displayName: originalPath };
    });

    // 跳转到编辑页面，传递当前媒体库数据
    history.push({
      pathname: `/${path}/createLibrary`,
      state: {
        isEdit: true,
        libraryData: {
          lib_id: item.lib_id,
          name: item.title,
          tv_visable: item.tv_visable || 0,
          scan_path: item.scan_path || [],
          share2who_list: item.share2who_list || [],
          // 传递预处理的显示路径
          processedScanPath
        },
        // 传递LibraryManagement的state信息，以便CreateLibrary能够正确返回
        libraryManagementState: location.state
      }
    });
  }, [history, path, storagePools, webDAVConfig]);

  // 初始化存储池信息
  useEffect(() => {
    fetchPoolInfo();
  }, [fetchPoolInfo]);

  // 初始化媒体库信息
  useEffect(() => {
    // 初始加载显示loading
    fetchLibraryData(true);
  }, [fetchLibraryData]);

  // 处理创建媒体库按钮点击
  const handleCreateLibrary = useCallback(() => {
    // 检查是否已达到创建上限
    if (myselfCreate.length >= 5) {
      modalShow(
        '',
        (
          <>
            <div style={{ fontFamily: 'MiSans', fontWeight: '400', textAlign: 'center', fontSize: '16px', color: 'var(--text-color)', padding: '0 20px 28px 28px' }}>
              你创建的媒体库已满5个，无法继续，请删除后重试
            </div>
          </>
        ),
        () => null,
        () => null,
        false,
        {
          okBtnStyle: { display: 'none' },
          cancelBtnStyle: { width: px2rem('340px'), margin: 0 },
          position: 'bottom',
          bottom: '80px'
        }
      );
      return;
    }

    // 如果未达到上限，正常跳转到创建页面
    history.push({
      pathname: `/${path}/createLibrary`,
      state: {
        isEdit: false,
        libraryDataCount: myselfCreate.length,
        // 传递LibraryManagement的state信息，以便CreateLibrary能够正确返回
        libraryManagementState: location.state
      }
    });
  }, [myselfCreate.length, history, path]);

  // 自定义返回函数
  const handleBack = useCallback(() => {
    // 如果是从 Library 页面跳转过来的，且有 lib_id，则返回到对应的 Library 页面
    if (location.state?.fromLibrary && location.state?.lib_id && location.state?.title) {
      history.push({
        pathname: `/${path}/library`,
        state: {
          lib_id: location.state.lib_id,
          title: location.state.title
        } as any
      });
    } else {
      // 其他情况（直接从All页面进入）返回到All页面
      history.push(`/${path}/all`);
    }
  }, [history, location.state, path]);

  const rightSize = useMemo(() => {
    return (
      <div className={styles.right}>
        <PreloadImage src={isDarkMode ? addIcon_dark : addIcon_light} alt='add' style={{ width: '40px', height: '40px' }} onClick={handleCreateLibrary} />
      </div>
    )
  }, [handleCreateLibrary, isDarkMode])

  return (
    <>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} styles={{ paddingLeft: '15px', backgroundColor: 'var(--background-color)' }} onBack={handleBack} right={rightSize} />
      <div className={styles.container}>
        <div className={styles.tabsHeader}>
          <span className={styles.tabsHeader_span}>媒体库管理</span>
        </div>
        <div className={styles.content}>
          <div className={styles.library_content_container}>
            <span className={styles.library_title}>{`我创建的（${myselfCreate.length}个）`}</span>
            {myselfCreate.length > 0 ? (
              myselfCreate.map((item, index) => (
                <ListCard
                  key={item.title + index}
                  {...item}
                  type='more'
                  onEdit={handleEditLibrary}
                />
              ))
            ) : (
              !loading && (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 20px',
                  color: '#999',
                  fontSize: '14px'
                }}>
                  暂无媒体库，点击右上角"+"创建新的媒体库
                </div>
              )
            )}
          </div>
          <div className={styles.divider_container}>
            <Divider />
          </div>
          <div className={styles.library_content_container}>
            <span className={styles.library_title}>{`他人分享给我的（${otherShare.length}个）`}</span>
            {otherShare.length > 0 ? (
              otherShare.map((item, index) => (
                <ListCard
                  key={item.title + index}
                  {...item}
                  type='more'
                  isDel={true}
                  onDelete={handleExitLibraryShare}
                />
              ))
            ) : (
              !loading && (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 20px',
                  color: '#999',
                  fontSize: '14px'
                }}>
                  暂无分享的媒体库
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default LibraryManagement;