@keyframes collapse {
  from {
    max-height: 1000px;
  }
  to {
    max-height: 0;
  }
}

@keyframes expand {
  from {
    max-height: 0;
  }
  to {
    max-height: 1000px;
  }
}

.collapse {
  animation: collapse 0.01s forwards;
}
.expand {
  animation: expand 0.01s forwards;
}

// 粘性筛选栏直接固定在视口顶部
.sticky_header {
  position: sticky;
  top: 0;
  z-index: 10; // 提高层级
}

.collapse_filter_sticky_header_bg {
  background-color: var(--background-color);
}

.collapse_filter_container {
  user-select: none;
  overflow: hidden;

  .collapse_filter_type_scroll_container {
    width: 100%;
    overflow-x: auto; /* 启用横向滚动 */
    overflow-y: hidden; /* 隐藏纵向滚动 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
  }
}

.collapse_filter_type_container {
  display: flex;
  gap: 10px; /* item间距 */
  padding: 5px 0;

  .collapse_filter_type_content {
    gap: 0 10px;
    min-width: 50%; /* 至少占满半行 */
  }

  .collapse_filter_type_content.app {
    display: grid;
    grid-template-columns: repeat(11, 65px); /* 每行 10 列 */
    gap: 10px 10px;
  }

  .collapse_filter_type_content.pc {
    display: flex;
  }
}

.collapse_filter_type_item,
.collapse_filter_type_item_label {
  min-width: 65px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;

  font-family: MiSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  color: var(--subtitle-text-color);

  border-radius: 6px;
  cursor: pointer;
}

.collapse_filter_type_item_img {
  height: 17px;
}

.collapse_filter_type_item.app,
.collapse_filter_type_item_label.app {
  background-color: var(--side-bar-background-color);
}

.collapse_filter_type_item_label {
  gap: 8px;
  span {
    display: block;
    flex: 1;
    padding: 0 6px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.selected {
  background-color: var(--primary-btn-background-color);
  color: var(--primary-color);
}

.disable {
  width: 100%;
  opacity: 0.5;
  user-select: none;
  pointer-events: none;
  position: relative;
}

.collapse_filter_type_more {
  background-color: var(--background-color) !important;
  color: var(--list-value-text-color);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}
